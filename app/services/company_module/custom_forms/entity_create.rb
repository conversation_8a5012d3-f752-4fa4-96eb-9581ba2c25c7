module CompanyModule
  module CustomForms
    class EntityCreate
      include HandleCompanyCacheKeys

      attr_accessor :company_module_data, :payload, :new_user, :current_user, :from_automated_task, :form_fields, :custom_form, :error,
                    :company, :is_bulk_action, :preloaded_data, :automated_task, :deleted_values, :preloaded_company_users_by_user_id, :users_by_email

      def initialize(options = {})
        self.company_module_data = options[:company_module_data]
        self.company = options[:company]
        self.new_user = options[:new_user]
        self.current_user = options[:current_user]
        self.from_automated_task = options[:from_automated_task]
        self.form_fields = options[:form_fields]
        self.custom_form = options[:custom_form]
        self.is_bulk_action = options[:is_bulk_action]
        self.preloaded_data = options[:preloaded_data] || {}
        self.automated_task = options[:automated_task]
        self.deleted_values = options[:deleted_values] || []
        self.preloaded_company_users_by_user_id = options[:preloaded_company_users_by_user_id]
        self.users_by_email = options[:users_by_email] || {}
      end

      def call
        ActiveRecord::Base.transaction do
          @user_fields_ids = []
          entity_form_fields = form_fields.present? ? form_fields : form.custom_form_fields

          if form.company_module == "company_user"
            custom_entity.company_guests = preloaded_data[:guests]
            custom_entity.helpdesk_cf_ids = preloaded_data[:helpdesk_cf_ids]
            custom_entity.integration_users = preloaded_data[:integration_users]
            set_company_user_details(entity_form_fields)
          end

          if ((form.company_module == "company_user" && custom_entity.save) || custom_entity.valid?) && payload[:values]
            entity_class_name = custom_entity.class.name

            if form.company_module == 'helpdesk'
              custom_entity.source = payload['source'] if payload['source'].present?
              custom_entity.secondary_source = company_module_data.secondary_source
              custom_entity.mute_notification = payload['muteNotification'] if payload['muteNotification'].present?
            end

            custom_entity.comment_id = payload['commentId'] if payload['commentId'].present?
            custom_entity.skip_automated_tasks if custom_entity.respond_to?(:skip_automated_tasks)

            if (entity_class_name === 'HelpTicket')
              custom_entity.current_user = company_module_data.current_user unless from_automated_task
            end

            if ['CompanyMember', 'CompanyUser'].exclude?(entity_class_name)
              custom_entity.save!
            end
            AttachmentUpload.where(id: company_module_data.attachment_ids).update_all(attachable_id: custom_entity.id) if company_module_data.attachment_ids.present?

            populate_values(entity_form_fields)
            populate_missing_values(entity_form_fields)
            email = @custom_entity.creator_ids.first if entity_class_name === 'HelpTicket'

            CustomFormValue.import! values
            values.each do |val|
              val.populate_value_str_tokens
              val.set_module_status_value if val.is_helpticket_status_field?
              val.add_module_assigned_to_value if val.is_helpticket_assigned_to_field?
              val.add_module_creator_value if val.is_helpticket_creator_field?
              val.process_related_field if val.is_help_ticket_list_field?
              val.update_module_group_members if val.is_helpticket_people_list_field?
              val.set_description_value if val.is_helpticket_description_field?
              val.set_subject_value if val.is_helpticket_subject_field?
              val.set_priority_value if val.is_helpticket_priority_field?
            end

            tasks = []
            case entity_class_name
            when "HelpTicket"
              custom_entity.create_source_activity(automated_task)
              creator_id = @custom_entity.creator_ids.first
              workspace_id = custom_entity.workspace_id
              tasks = AutomatedTasks::AutomatedTask
                      .where(workspace_id: workspace_id, disabled_at: nil)
                      .joins(task_events: :event_type)
                      .includes(task_events: :event_type)
                      .where(automated_tasks_event_types: { event_class: 'TicketRemainsInSameState' })
                      .uniq
            when "CompanyUser", "CompanyMember"
              create_user_activity unless is_bulk_action
            end

            values_with_attachments.each do |entry|
              attachment = CustomFormAttachment.find_by(id: entry[:attachment_ids])
              old_custom_form_value = attachment.custom_form_value
              attachment.update_attribute('custom_form_value_id', entry[:form_value].id)
              old_custom_form_value&.destroy
              attachment.contributor_id = company_user&.contributor_id
              attachment.skip_callbacks = true
              create_attachment_comment(attachment, email, creator_id)
            end

            if custom_entity&.linkable || tasks.present?
              custom_entity.custom_form_values.includes(:module => :linkable).each do |cfv|
                update_custom_entity_linkable(custom_entity, entity_class_name, cfv)
                cfv.schedule_ticket_idle_state_automated_tasks(tasks)
              end
            end
            custom_entity.process_automated_tasks if custom_entity.respond_to?(:process_automated_tasks)
            custom_entity.fire_event_for_automation if custom_entity.respond_to?(:fire_event_for_automation)

            custom_entity.sync_attachments(self, custom_entity) if custom_entity.respond_to?(:sync_attachments)
          end
          ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
        rescue => e
          Rails.logger.error("Transaction failed: #{e.message}")
          self.error = e
          raise ActiveRecord::Rollback
        end

        self
      end

      def company
        @company || self.company_module_data.company
      end

      def payload
        @payload ||= JSON.parse(company_module_data.payload).with_indifferent_access
      end

      def custom_form_id
        @custom_form_id ||= self.company_module_data.custom_form_id
      end

      def company_user
        @company_user ||= self.company_module_data.company_user
      end

      def create_activity
        created_by_values = @custom_entity.custom_form_values.select{ |val| val.custom_form_field.name === "created_by" && val.custom_form_field.field_attribute_type == "people_list"}
        contributor_ids = created_by_values.map(&:value)
        contributor_ids.each do |id|
          HelpTicketActivity.create(activity_params(id)) if id.is_a?(Integer)
        end
      end

      def create_user_activity
        activity_params = {
          activity_type: CompanyUserActivity.activity_types["created"],
          owner_id: current_user.id,
        }
        custom_entity.activities.create(activity_params)
      end

      def create_bulk_user_activity(company_users)
        return unless company_users.present?

        company_user_activities = []
        company_users.each do |cu|
          user_source = DiscoveredUser.find_by(email: cu.email, company_id: cu.company_id)&.discovered_user_sources&.pluck(:source)&.first          
          company_user_activities << {
            activity_type: CompanyUserActivity.activity_types["imported"],
            owner_id: current_user.id,
            company_user_id: cu.id,
            source: user_source
          }
        end
        CompanyUserActivity.insert_all(company_user_activities)
      end

      def allowed_field_ids(entity_form_fields)
        @allowed_field_ids ||= entity_form_fields.map(&:id) - @user_fields_ids
      end

      def populate_missing_values(entity_form_fields)
        # Fill in any missing values with defaults where they exist
        entity_form_fields.find_each do |form_field|
          if missing_field_ids.include?(form_field.id)
            if form_field.default_value.present? && !phone_number_default(form_field)
              if form_field.singular?
                form_value = custom_entity.custom_form_values.new(custom_form_id: custom_entity.custom_form_id,
                                                                  custom_form_field_id: form_field.id,
                                                                  value: form_field.default_value)
                form_value.custom_form_id = custom_entity.custom_form_id
                form_value.company_id = custom_entity.company_id
                values << form_value
              else
                JSON.parse(form_field.default_value).each do |val|
                  if form_field.people_list? && val == 'current'
                    val = @custom_entity.creator_ids.first
                  end
                  next if deleted_values.any? { |deleted| deleted["customFormFieldId"] == form_field.id && deleted["value"] === val }

                  form_value = custom_entity.custom_form_values.new(custom_form_id: custom_entity.custom_form_id,
                                                                    custom_form_field_id: form_field.id,
                                                                    value: val)
                  form_value.custom_form_id = custom_entity.custom_form_id
                  form_value.company_id = custom_entity.company_id
                  values << form_value
                end
              end
            end
          end
        end
      end

      def phone_number_default(field)
        field.field_attribute_type == "phone" && field.default_value.split(',')[1].blank?
      end

      def custom_entity
        @custom_entity ||= begin
          e = entity_class.new(company: company, custom_form_id: custom_form_id)
          e.workspace = form.workspace if e.respond_to?(:workspace=)
          e
        end
      end

      def entity_class
        company_modules = {
          "asset": "ManagedAsset",
          "contract": "Contract",
          "vendor": "Vendor",
          "helpdesk": "HelpTicket",
          "telecom": "TelecomProvider",
          "company_user": "CompanyUser",
          "location": "Location",
        }
        company_modules[form.company_module.to_sym].constantize
      end

      def form
        @form ||= custom_form || CustomForm.find_by(id: custom_form_id)
      end

      def values
        @values ||= []
      end

      def missing_field_ids
        @missing_field_ids
      end

      def values_with_attachments
        @values_with_attachments ||= []
      end

      def ids
        @ids ||= []
      end

      def clean_word_email(raw)
        text = Nokogiri::HTML.fragment(raw).text
        text = text.gsub(/\\n/, "\n").gsub(/\.[\w.-]+\s*\{[^}]*\}/m, '').gsub(/\s+/, ' ').strip
        text[/\b([A-Z][^.!?]+\.\s+.*)/m, 1]&.strip || text
      end
      
      def populate_values(entity_form_fields)

        payload[:values].each do |value|
          field_id = value["customFormFieldId"] || value["custom_form_field_id"]
          field = status_priority_options.find { |f| f[:id] == field_id }
          value_str = value["valueStr"] || value["value_str"]
          if payload[:source] == "splitted" && value_str.present?
            cleaned = clean_word_email(value_str)
            value_str = cleaned.presence || value_str
          end
          value_int = value["valueInt"] || value["value_int"]
          raise "The selected #{field[:type]} option #{value_str} has been removed" if field.present? && !(field[:options].include?(value_str))
          if allowed_field_ids(entity_form_fields).include?(field_id) && is_value_present?(value_int, value_str, value)
            if value_int.is_a?(Array)
              value_int.each { |i| add_value(value, field_id, value_str, i) }
            else
              add_value(value, field_id, value_str, value_int)
            end
          end
        end

        @missing_field_ids = allowed_field_ids(entity_form_fields) - ids
      end

      def is_value_present?(value_int, value_str, value)
        value_int.present? || value_str.present? || value["attachmentId"].present? || value["attachment_id"].present?
      end

      def add_value(value, field_id, value_str, value_int)
        form_value = custom_entity.custom_form_values.new(
                      custom_form_field_id: field_id,
                      value_str: value_str.is_a?(String) ? value_str&.gsub("&nbsp;", "") : value_str,
                      value_int: value_int)

        if form_value.value_str && form_value.custom_form_field.people_list?
          found_user = users_by_email&.[](form_value.value_str.strip.downcase) || User.find_by_cache(email: email)
          if found_user
            found_company_user = @company_users_by_user_id&.[](found_user.id) || CompanyUser.find_by_cache(user_id: found_user.id, company_id: company.id)
          end
        end
        if form_value.custom_form_field.status? && form_value.value_str == 'Closed'
          custom_entity.update_columns(closed_at: DateTime.now, days_opened: 0)
        end
        form_value.creator_id = form_value.custom_form_field.field_attribute_type == "status" ? company_user&.id : nil
        if value["attachment_id"].present? && from_automated_task
          new_attachment_id = company_module_data.new_attachment_ids(value["attachment_id"])
          values_with_attachments << { form_value: form_value, attachment_ids: new_attachment_id }
        elsif value['attachmentId'].present? || value["attachment_id"].present?
          values_with_attachments << { form_value: form_value, attachment_ids: value['attachmentId'] || value["attachment_id"] }
        end
        form_value.custom_form_id = custom_entity.custom_form_id
        form_value.company_id = custom_entity.company_id
        ids << field_id.to_i
        values << form_value
      end

      def create_attachment_comment(att, email = nil, creator_id = nil)
        # Creates comment for every custom form attachment added
        custom_form_value = att.custom_form_value
        if !att.auto_add && custom_form_value.present? && custom_form_value.module_type == "HelpTicket"
          attachment = att.attachment
          attachment_url = attachment.url
          file_name = attachment.filename.to_s
          comment_body = "A new attachment was added: "
          if attachment.image?
            if file_name.downcase.end_with?('.tiff')
              comment_body += "<a href=#{attachment_url}>#{file_name}</a>"
            else
              comment_body += "<br><img src=#{attachment_url}></img>#{file_name}"
            end
          else
            comment_body += "<a href=#{attachment_url}>#{file_name}</a>"
          end
          
          current_company = custom_form_value.company
          if email
            cont_id = company_user_cache(current_company, email)&.contributor_id
          elsif creator_id
            guest = Guest.find_by(contributor_id: creator_id)
            cont_id = creator_id
          end

          comment_params = {
            comment_body: comment_body,
            help_ticket_id: att.module.id,
            custom_form_attachment_id: att.id,
            contributor_id: att&.contributor_id ? att&.contributor_id : cont_id,
            source: email || guest&.email ? :email : :manually_added,
            email: email || guest&.email
          }
          comment = att.module.help_ticket_comments.new(comment_params)
          comment.skip_automated_tasks
          comment.save!
        end
      end

      def set_company_user_details(entity_form_fields, company_users_by_user_id = nil)
        @user_fields_ids = if entity_form_fields.respond_to?(:where)
                              entity_form_fields.where("name IN ('first_name', 'last_name', 'email')").ids
                           else
                              entity_form_fields.select { |f| ['first_name', 'last_name', 'email'].include?(f.name) }.map(&:id)
                           end
        custom_entity.created_by = current_user if current_user # used for reward points

        if new_user.present?
          custom_entity.user = new_user
          existing_company_user =
            (self.preloaded_company_users_by_user_id && self.preloaded_company_users_by_user_id[new_user.id]) ||
            (company_users_by_user_id && company_users_by_user_id[new_user.id]) ||
            CompanyUser.find_by_cache(user_id: new_user.id, company_id: company.id)
          if existing_company_user
            @custom_entity = existing_company_user
            filtered_ticket_emails = preloaded_data[:ticket_emails]&.select { |email| email.from.include?(@custom_entity.email) }
            custom_entity.convert_ticket_emails(filtered_ticket_emails)
          end
        end

        if payload["mfaSettings"].present?
          mfa_params = JSON.parse(payload["mfaSettings"])
          @custom_entity.assign_attributes(mfa_params)
        end
        custom_entity.current_user = current_user
      end

      def update_custom_entity_linkable(custom_entity, entity_class_name, cfv)
        entity_linkable = custom_entity&.linkable

        if entity_class_name == "Location"
          if cfv.custom_form_field&.name == 'name' && entity_linkable
            linkable = entity_linkable
            linkable.name = cfv.value_str
            linkable.save!
          end
        else
          if cfv.custom_form_field&.name == 'subject' && entity_linkable
            linkable = entity_linkable
            linkable.name = cfv.value_str
            linkable.save!
          end
        end
      end

      def contributors
        @contributors ||= preloaded_data[:contributors]
      end

      def status_priority_options
        @status_priority_options ||= begin
          fields = form_fields.presence || form.custom_form_fields
          fields.select { |cff| ['status', 'priority'].include?(cff.field_attribute_type) }.map do |cff|
            { id: cff.id, options: JSON.parse(cff.options).pluck('name'), type: cff.field_attribute_type }
          end
        end
      end
    end
  end
end
